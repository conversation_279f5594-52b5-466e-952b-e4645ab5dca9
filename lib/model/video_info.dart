import 'package:fldanplay/model/history.dart';
import 'package:fldanplay/utils/crypto_utils.dart';

class VideoInfo {
  // 视频文件的真实地址
  String currentVideoPath;
  // 虚拟路径(1/video.mp4)
  String virtualVideoPath;
  Map<String, String> headers;
  HistoriesType historiesType;
  String? storageKey;
  int videoIndex;
  bool canSwitch = false;
  late String uniqueKey;

  VideoInfo({
    required this.currentVideoPath,
    required this.virtualVideoPath,
    this.headers = const {},
    required this.historiesType,
    this.storageKey,
    this.videoIndex = 0,
    this.canSwitch = false,
  }) {
    uniqueKey = CryptoUtils.generateVideoUniqueKey(virtualVideoPath);
  }

  String get videoName => currentVideoPath.split('/').last.split('.').first;

  String get videoFullName => currentVideoPath.split('/').last;
}
