import 'package:hive_ce_flutter/hive_flutter.dart';

enum HistoriesType { network, local, storage }

class History extends HiveObject {
  String uniqueKey;
  int duration;
  int position;
  String? url;
  HistoriesType type;
  String? storageKey;
  int updateTime;

  History({
    required this.uniqueKey,
    required this.duration,
    required this.position,
    this.url,
    required this.type,
    this.storageKey,
    required this.updateTime,
  });

  History copyWith({
    int? id,
    String? uniqueKey,
    int? duration,
    int? position,
    String? url,
    String? headers,
    HistoriesType? type,
    String? storageKey,
    int? updateTime,
  }) {
    return History(
      uniqueKey: uniqueKey ?? this.uniqueKey,
      duration: duration ?? this.duration,
      position: position ?? this.position,
      url: url ?? this.url,
      type: type ?? this.type,
      storageKey: storageKey ?? this.storageKey,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  factory History.fromJson(Map<String, dynamic> json) {
    return History(
      uniqueKey: json['uniqueKey'] as String,
      duration: json['duration'] as int,
      position: json['position'] as int,
      url: json['url'] as String?,
      type: HistoriesType.values[json['type'] as int],
      storageKey: json['storageKey'] as String?,
      updateTime: json['updateTime'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uniqueKey': uniqueKey,
      'duration': duration,
      'position': position,
      'url': url,
      'type': type.index,
      'storageKey': storageKey,
      'updateTime': updateTime,
    };
  }
}
