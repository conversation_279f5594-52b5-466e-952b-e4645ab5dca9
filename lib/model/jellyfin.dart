enum CollectionType { tvshows, movies, none }

enum MediaType {
  movie('Movie'),
  series('Series'),
  none('nnoe');

  final String name;
  const MediaType(this.name);
}

class CollectionItem {
  final String name;
  final CollectionType type;
  final String id;
  CollectionItem({
    required this.name,
    required this.id,
    this.type = CollectionType.none,
  });

  factory CollectionItem.fromJson(Map<String, dynamic> json) {
    return CollectionItem(
      name: json['Name'],
      id: json['Id'],
      type: CollectionType.values.firstWhere(
        (e) => e.name == json['CollectionType'],
        orElse: () => CollectionType.none,
      ),
    );
  }
}

class MediaItem {
  final String name;
  final String id;
  final MediaType type;
  MediaItem({required this.name, required this.id, this.type = MediaType.none});

  factory MediaItem.fromJson(Map<String, dynamic> json) {
    return MediaItem(
      name: json['Name'],
      id: json['Id'],
      type: MediaType.values.firstWhere(
        (e) => e.name == json['Type'],
        orElse: () => MediaType.none,
      ),
    );
  }
}
