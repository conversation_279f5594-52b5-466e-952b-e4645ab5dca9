import 'package:auto_orientation/auto_orientation.dart';
import 'package:fldanplay/model/video_info.dart';
import 'package:fldanplay/page/file_explorer.dart';
import 'package:fldanplay/page/history.dart';
import 'package:fldanplay/page/player/player.dart';
import 'package:fldanplay/page/root.dart';
import 'package:fldanplay/page/settings/appearance_settings.dart';
import 'package:fldanplay/page/settings/log_page.dart';
import 'package:fldanplay/page/settings/log_view.dart';
import 'package:fldanplay/page/settings/player_settings.dart';
import 'package:fldanplay/page/settings/settings.dart';
import 'package:fldanplay/page/settings/sync_settings.dart';
import 'package:fldanplay/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';

const String rootPath = '/';
const String fileExplorerPath = '/file-explorer';
const String historyPath = '/history';
const String videoPlayerPath = '/video-player';
const String settingsPath = '/settings';

// GoRouter configuration
final router = GoRouter(
  initialLocation: rootPath,
  routes: [
    GoRoute(path: rootPath, builder: (context, state) => const RootPage()),
    GoRoute(
      path: settingsPath,
      builder: (context, state) => const SettingsPage(),
      routes: [
        GoRoute(
          path: 'player',
          builder: (context, state) => const PlayerSettingsPage(),
          routes: [
            GoRoute(
              path: 'hardware-decoder',
              builder: (context, state) => const HardwareDecoderPage(),
            ),
          ],
        ),
        GoRoute(
          path: 'appearance',
          builder: (context, state) => const AppearanceSettingsPage(),
        ),
        GoRoute(
          path: 'webdav',
          builder: (context, state) => const SyncSettingsPage(),
        ),
        GoRoute(
          path: 'log',
          builder: (context, state) => const LogPage(),
          routes: [
            GoRoute(
              path: 'view',
              builder: (context, state) {
                final file = state.uri.queryParameters['file'] ?? '';
                return LogViewPage(fileName: file);
              },
            ),
          ],
        ),
      ],
    ),
    GoRoute(
      path: fileExplorerPath,
      builder: (context, state) {
        return FileExplorerPage(
          storageKey: state.uri.queryParameters['key'] ?? '',
          storageName: state.uri.queryParameters['name'] ?? '',
        );
      },
    ),
    GoRoute(
      path: historyPath,
      builder: (context, state) => const HistoryPage(),
    ),
    GoRoute(
      path: videoPlayerPath,
      pageBuilder: (context, state) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        AutoOrientation.landscapeAutoMode(forceSensor: true);
        final videoInfo = state.extra as VideoInfo;
        return CustomTransitionPage(
          child: Theme(
            data: zincDark.toApproximateMaterialTheme(),
            child: FTheme(data: zincDark, child: VideoPlayerPage(videoInfo)),
          ),
          transitionsBuilder: (_, animation, _, child) {
            return child;
          },
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
        );
      },
    ),
  ],
);
