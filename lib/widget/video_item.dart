import 'dart:io';

import 'package:fldanplay/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:path_provider/path_provider.dart';
import '../model/history.dart';

class FileImageEx extends FileImage {
  late final int fileSize;
  FileImageEx(File file, {double scale = 1.0}) : super(file, scale: scale) {
    fileSize = file.lengthSync();
  }

  @override
  // ignore: hash_and_equals
  bool operator ==(Object other) {
    if (other.runtimeType != runtimeType) return false;
    return other is FileImageEx &&
        other.file.path == file.path &&
        other.scale == scale &&
        other.fileSize == fileSize;
  }
}

class VideoItem extends StatelessWidget with FItemMixin {
  final History? history;
  final String name;
  final void Function() onPress;
  final void Function()? onLongPress;
  VideoItem({
    super.key,
    required this.history,
    required this.name,
    required this.onPress,
    this.onLongPress,
  });

  Future<Widget> _buildPerfix(History? history) async {
    if (history != null) {
      final directory = await getApplicationDocumentsDirectory();
      return ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Image(
          image: FileImageEx(
            File('${directory.path}/screenshots/${history.uniqueKey}'),
          ),
          fit: BoxFit.fitHeight,
          errorBuilder: (context, error, stackTrace) {
            return const Icon(FIcons.play, size: 50);
          },
        ),
      );
    }
    return const Icon(FIcons.play, size: 50);
  }

  @override
  Widget build(BuildContext context) {
    double progress = 0;
    int progressPercent = 0;
    String lastWatchTime = '';
    if (history != null) {
      progress =
          history!.duration > 0
              ? (history!.position / history!.duration).clamp(0.0, 1.0)
              : 0.0;
      progressPercent = (progress * 100).round();
      lastWatchTime = formatLastWatchTime(history!.updateTime);
    }
    final subtitleStyle = context.theme.itemStyle.contentStyle.subtitleTextStyle
        .resolve({});
    return FItem(
      prefix: SizedBox(
        width: 95,
        height: 65,
        child: FutureBuilder(
          future: _buildPerfix(history),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const CircularProgressIndicator();
            }
            if (snapshot.hasData) {
              return snapshot.data!;
            }
            return const Icon(FIcons.file, size: 50);
          },
        ),
      ),
      title: ConstrainedBox(
        constraints: const BoxConstraints(minHeight: 65),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FTooltip(
              tipBuilder:
                  (context, controller) => Container(
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width - 50,
                    ),
                    child: Text(name),
                  ),
              child: Text(
                name,
                style: context.theme.typography.base,
                maxLines: 2,
              ),
            ),
            history != null
                ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            style: subtitleStyle,
                            '观看进度: ${formatTime(history!.position, history!.duration)}',
                          ),
                        ),
                        Text(style: subtitleStyle, '$progressPercent%'),
                      ],
                    ),
                    const SizedBox(height: 4),
                    FProgress(
                      value: progress,
                      duration: Duration.zero,
                      style:
                          (style) => style.copyWith(
                            constraints: style.constraints.copyWith(
                              minHeight: 4,
                              maxHeight: 4,
                            ),
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(style: subtitleStyle, lastWatchTime),
                  ],
                )
                : Text(style: subtitleStyle, '未观看'),
          ],
        ),
      ),
      onPress: onPress,
      onLongPress: onLongPress,
    );
  }
}
