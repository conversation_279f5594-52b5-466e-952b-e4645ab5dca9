import 'package:dio/dio.dart';
import 'package:fldanplay/model/jellyfin.dart';

class JellyfinApiUtils {
  Dio getDio(String url, {String? token}) {
    String auth =
        'Authorization: MediaBrowser Client="fldanplay", Device="flutter", DeviceId="flutter", Version="0.01"';
    if (token != null) {
      auth += ', Token="$token"';
    }
    return Dio(BaseOptions(baseUrl: url, headers: {'Authorization': auth}));
  }

  Future<String> login(Dio dio, String username, String password) async {
    try {
      final response = await dio.post(
        '/Users/<USER>',
        data: {'Username': username, 'Pw': password},
      );
      return response.data['AccessToken'];
    } on DioException catch (e) {
      throw Exception('登录失败: ${e.message}');
    }
  }

  // Future<void> userInfo(Dio dio) async {
  //   try {
  //     final response = await dio.get('/Users/<USER>');
  //   } on DioException catch (e) {
  //     throw Exception('获取用户信息失败: ${e.message}');
  //   }
  // }

  Future<List<CollectionItem>> getUserViews(Dio dio) async {
    try {
      final response = await dio.get('/UserViews');
      List<CollectionItem> res = [];
      for (var item in response.data['Items']) {
        res.add(CollectionItem.fromJson(item));
      }
      return res;
    } on DioException catch (e) {
      throw Exception('获取用户视图失败: ${e.message}');
    }
  }

  Future<List<MediaItem>> getItems(Dio dio, String parentId) async {
    try {
      final response = await dio.get(
        '/Items',
        queryParameters: {'parentId': parentId},
      );
      List<MediaItem> res = [];
      for (var item in response.data['Items']) {
        res.add(MediaItem.fromJson(item));
      }
      return res;
    } on DioException catch (e) {
      throw Exception('获取用户视图失败: ${e.message}');
    }
  }

  Future<void> getItem(Dio dio, String itemId) async {
    try {
      final response = await dio.get('/Items/$itemId', queryParameters: {});
    } on DioException catch (e) {
      throw Exception('获取用户视图失败: ${e.message}');
    }
  }
}
